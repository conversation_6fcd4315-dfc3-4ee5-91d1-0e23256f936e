# 商户模块 (Merchant Module)

## 概述

商户模块包含商户列表管理和任务列表管理功能，提供了一套完整的商户信息管理和任务跟进系统。

## 模块结构

```
merchant/
├── components/           # 共用组件
│   ├── CommTable/       # 通用表格组件
│   ├── BatchAssignModal/# 批量分配组件
│   ├── TaskAssigneeModal/# 任务分配组件
│   └── HistoryTable/    # 跟进历史表格组件
├── merchant-list/       # 商户列表页面
├── task-list/          # 任务列表页面
└── README.md           # 模块文档
```

## 共用组件

### CommTable - 通用表格组件

提供统一的表格样式和分页配置，用于显示各种数据列表。

**特性：**
- 统一的分页配置
- 响应式设计
- 可自定义列配置
- 支持加载状态

**使用示例：**
```tsx
import CommTable from '../components/CommTable/index.web';

<CommTable
  dataSource={data}
  columns={columns}
  loading={loading}
  pageSize={pageSize}
  current={currentPage}
  total={total}
  onChange={handlePageChange}
/>
```

### BatchAssignModal - 批量分配组件

通用的批量分配模态框，支持任务分配、商户分配等场景。

**特性：**
- 支持多种分配场景
- 可自定义表格列
- 统一的分配人选择界面
- 数据验证和错误处理

**使用示例：**
```tsx
import BatchAssignModal from '../components/BatchAssignModal/index.web';

<BatchAssignModal
  visible={visible}
  confirmLoading={loading}
  selectedItems={selectedItems}
  assigneeOptions={assigneeOptions}
  title="批量分配任務"
  assigneeLabel="選擇跟進人"
  itemTypeName="任務"
  tableColumns={customColumns}
  onCancel={handleCancel}
  onSubmit={handleSubmit}
/>
```

### TaskAssigneeModal - 任务分配组件

专门用于单个任务分配的模态框组件。

**特性：**
- 显示任务基本信息
- 分配人选择
- 分配历史记录
- 备注功能

**使用示例：**
```tsx
import TaskAssigneeModal from '../components/TaskAssigneeModal/index.web';

<TaskAssigneeModal
  visible={visible}
  taskData={taskData}
  currentAssignee={currentAssignee}
  assigneeOptions={assigneeOptions}
  assignHistory={assignHistory}
  onCancel={handleCancel}
  onSave={handleSave}
/>
```

### HistoryTable - 跟进历史表格组件

专门用于显示跟进历史记录的表格组件。

**特性：**
- 跟进状态标签显示
- 详情信息提示
- 时间格式化显示
- 支持加载状态

**使用示例：**
```tsx
import HistoryTable from '../components/HistoryTable/index.web';

<HistoryTable
  list={followUpRecords}
  loading={loading}
/>
```

## 页面功能

### 商户列表 (merchant-list)

- 商户信息展示和管理
- 批量操作（分配BD等）
- 商户状态管理
- 响应式表格设计

### 任务列表 (task-list)

- 任务跟进管理
- 批量任务分配
- 任务状态跟踪
- 跟进历史记录

## 组件抽离说明

为了提高代码复用性和维护性，我们将以下组件从各个页面中抽离到 `merchant/components` 目录：

1. **CommTable**: 从 `task-list/components/CommTable` 抽离
2. **BatchAssignModal**: 整合了 `task-list` 和 `merchant-list` 中的批量分配功能
3. **TaskAssigneeModal**: 从 `task-list/components/TaskAssigneeModal` 抽离并增强
4. **HistoryTable**: 从 `task-list/components/HistoryTable` 抽离

## 类型定义

共用组件的类型定义分别在各自的组件文件中导出，包括：

- `ICommTable`: 通用表格组件属性
- `AssigneeOption`: 分配人选项接口
- `BatchAssignFormData`: 批量分配表单数据
- `BatchAssignModalProps`: 批量分配组件属性
- `TaskAssignFormData`: 任务分配表单数据
- `TaskAssigneeModalProps`: 任务分配组件属性
- `FollowUpRecord`: 跟进记录接口
- `HistoryTableProps`: 跟进历史表格组件属性

## 使用注意事项

1. 所有共用组件都支持TypeScript类型检查
2. 组件设计遵循响应式原则，支持移动端显示
3. 批量分配组件支持数据适配，可以处理不同的数据结构
4. 建议在使用前查看组件的类型定义以了解完整的API
