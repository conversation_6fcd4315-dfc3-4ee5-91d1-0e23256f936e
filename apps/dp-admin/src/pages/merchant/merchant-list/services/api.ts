/**
 * 商户列表相关 API 接口
 */

import type {
  BDOption,
  MerchantData,
  MerchantStatus,
  OptionData,
  SearchFormData,
} from '../types';

/**
 * 获取商户列表
 */
export const getMerchantList = async (
  params: {
    pageNum: number;
    pageSize: number;
  } & SearchFormData
): Promise<[boolean, { list: MerchantData[]; total: number } | null]> => {
  try {
    // 模拟 API 调用
    console.log('获取商户列表参数:', params);

    // 模拟数据
    const mockData: MerchantData[] = [
      {
        id: '1',
        merchantId: 'GoogleID',
        merchantStatus: 'APPROVED' as MerchantStatus,
        category: '餐饮',
        imageUrl: 'https://example.com/image1.jpg',
        city: '台北市',
        district: '信义区',
        businessArea: '信义商圈',
        merchantName: '美味餐厅',
        address: '台北市信义区信义路五段7号',
        businessStatus: 'OPEN',
        brand: '美味品牌',
        level: 'A',
        unifiedCode: '12345678',
        franchiseType: 'DIRECT',
        bd: '张三',
        updatedAt: '2025-01-22 15:30',
        joinedAt: '2025-01-20 10:00',
      },
      {
        id: '2',
        merchantId: '38343436',
        merchantStatus: 'PENDING' as MerchantStatus,
        category: '零售',
        imageUrl: 'https://example.com/image2.jpg',
        city: '台北市',
        district: '大安区',
        businessArea: '东区商圈',
        merchantName: '便利商店',
        address: '台北市大安区忠孝东路四段181号',
        businessStatus: 'OPEN',
        brand: '便利品牌',
        level: 'B',
        unifiedCode: '87654321',
        franchiseType: 'FRANCHISE',
        bd: '李四',
        updatedAt: '2025-01-22 14:20',
        joinedAt: '2025-01-21 09:30',
      },
    ];

    return [true, { list: mockData, total: 100 }];
  } catch (error) {
    console.error('获取商户列表失败:', error);
    return [false, null];
  }
};

/**
 * 获取 BD 选项
 */
export const getBDOptions = async (): Promise<[boolean, BDOption[] | null]> => {
  try {
    const mockData: BDOption[] = [
      { value: 'zhang_san', label: '张三' },
      { value: 'li_si', label: '李四' },
      { value: 'wang_wu', label: '王五' },
    ];
    return [true, mockData];
  } catch (error) {
    console.error('获取 BD 选项失败:', error);
    return [false, null];
  }
};

/**
 * 获取商户状态选项
 */
export const getMerchantStatusOptions = async (): Promise<
  [boolean, OptionData[] | null]
> => {
  try {
    const mockData: OptionData[] = [
      { value: 'PENDING', label: '待审核' },
      { value: 'APPROVED', label: '已通过' },
      { value: 'REJECTED', label: '已驳回' },
      { value: 'DISABLED', label: '已停用' },
    ];
    return [true, mockData];
  } catch (error) {
    console.error('获取商户状态选项失败:', error);
    return [false, null];
  }
};

/**
 * 获取商户类目选项
 */
export const getCategoryOptions = async (): Promise<
  [boolean, OptionData[] | null]
> => {
  try {
    const mockData: OptionData[] = [
      { value: 'restaurant', label: '餐饮' },
      { value: 'retail', label: '零售' },
      { value: 'service', label: '服务' },
      { value: 'entertainment', label: '娱乐' },
    ];
    return [true, mockData];
  } catch (error) {
    console.error('获取商户类目选项失败:', error);
    return [false, null];
  }
};

/**
 * 获取城市选项
 */
export const getCityOptions = async (): Promise<
  [boolean, OptionData[] | null]
> => {
  try {
    const mockData: OptionData[] = [
      {
        value: 'taipei',
        label: '台北市',
        children: [
          { value: 'xinyi', label: '信义区' },
          { value: 'daan', label: '大安区' },
          { value: 'zhongshan', label: '中山区' },
        ],
      },
      {
        value: 'taichung',
        label: '台中市',
        children: [
          { value: 'west', label: '西区' },
          { value: 'north', label: '北区' },
          { value: 'south', label: '南区' },
        ],
      },
    ];
    return [true, mockData];
  } catch (error) {
    console.error('获取城市选项失败:', error);
    return [false, null];
  }
};

/**
 * 获取品牌选项
 */
export const getBrandOptions = async (): Promise<
  [boolean, OptionData[] | null]
> => {
  try {
    const mockData: OptionData[] = [
      { value: 'brand1', label: '美味品牌' },
      { value: 'brand2', label: '便利品牌' },
      { value: 'brand3', label: '服务品牌' },
    ];
    return [true, mockData];
  } catch (error) {
    console.error('获取品牌选项失败:', error);
    return [false, null];
  }
};

/**
 * 获取等级选项
 */
export const getLevelOptions = async (): Promise<
  [boolean, OptionData[] | null]
> => {
  try {
    const mockData: OptionData[] = [
      { value: 'A', label: 'A级' },
      { value: 'B', label: 'B级' },
      { value: 'C', label: 'C级' },
      { value: 'D', label: 'D级' },
    ];
    return [true, mockData];
  } catch (error) {
    console.error('获取等级选项失败:', error);
    return [false, null];
  }
};

/**
 * 获取加盟类型选项
 */
export const getFranchiseTypeOptions = async (): Promise<
  [boolean, OptionData[] | null]
> => {
  try {
    const mockData: OptionData[] = [
      { value: 'DIRECT', label: '直营' },
      { value: 'FRANCHISE', label: '加盟' },
      { value: 'COOPERATION', label: '合作' },
    ];
    return [true, mockData];
  } catch (error) {
    console.error('获取加盟类型选项失败:', error);
    return [false, null];
  }
};

/**
 * 更新商户状态
 */
export const updateMerchantStatus = async (
  id: string | number,
  status: MerchantStatus
): Promise<[boolean, string | null]> => {
  try {
    console.log('更新商户状态:', { id, status });
    // 模拟 API 调用
    return [true, '状态更新成功'];
  } catch (error) {
    console.error('更新商户状态失败:', error);
    return [false, '状态更新失败'];
  }
};

/**
 * 批量分配商户
 */
export const batchAssignMerchants = async (
  merchantIds: (string | number)[],
  bdId: string
): Promise<[boolean, string | null]> => {
  try {
    console.log('批量分配商户:', { merchantIds, bdId });
    // 模拟 API 调用
    return [true, '批量分配成功'];
  } catch (error) {
    console.error('批量分配失败:', error);
    return [false, '批量分配失败'];
  }
};
