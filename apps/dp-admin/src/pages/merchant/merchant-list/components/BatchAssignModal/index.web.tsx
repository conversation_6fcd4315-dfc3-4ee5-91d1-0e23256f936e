import type { ColumnsType } from 'antd/es/table';
import React from 'react';

import CommonBatchAssignModal from '../../../components/BatchAssignModal/index.web';
import type { BatchAssignFormData } from '../../../components/BatchAssignModal/index.web';
import type { BDOption, MerchantData } from '../../types';

interface BatchAssignModalProps {
  /** 模态框可见性 */
  visible: boolean;
  /** 确认加载状态 */
  confirmLoading: boolean;
  /** 选中的商户 */
  selectedMerchants: MerchantData[];
  /** BD选项 */
  bdOptions: BDOption[];
  /** 取消回调 */
  onCancel: () => void;
  /** 提交回调 */
  onSubmit: (bdId: string) => void;
}

/**
 * 批量分配模态框组件
 */
const BatchAssignModal: React.FC<BatchAssignModalProps> = ({
  visible,
  confirmLoading,
  selectedMerchants,
  bdOptions,
  onCancel,
  onSubmit,
}) => {
  // 商户列表表格列定义
  const merchantColumns: ColumnsType<MerchantData> = [
    {
      title: '商戶ID',
      dataIndex: 'merchantId',
      key: 'merchantId',
      width: 120,
    },
    {
      title: '商戶名稱',
      dataIndex: 'merchantName',
      key: 'merchantName',
      width: 200,
      render: (text: string) => (
        <span className="line-clamp-1" title={text}>
          {text}
        </span>
      ),
    },
    {
      title: '當前BD',
      dataIndex: 'bd',
      key: 'bd',
      width: 120,
      render: (text: string) => (
        <span className="text-gray-600">
          {text || <span className="text-gray-400">未分配</span>}
        </span>
      ),
    },
  ];

  // 转换BD选项格式以适配通用组件
  const assigneeOptions = bdOptions.map((bd) => ({
    id: bd.value,
    name: bd.label,
    email: `${bd.value}@company.com`, // 假设的邮箱格式
    isCurrentUser: false,
  }));

  // 处理提交，适配不同的数据结构
  const handleSubmit = (data: BatchAssignFormData) => {
    onSubmit(data.assigneeId);
  };

  return (
    <CommonBatchAssignModal
      visible={visible}
      confirmLoading={confirmLoading}
      selectedItems={selectedMerchants}
      assigneeOptions={assigneeOptions}
      title="批量分配商戶"
      assigneeLabel="選擇BD"
      itemTypeName="商戶"
      tableColumns={merchantColumns}
      onCancel={onCancel}
      onSubmit={handleSubmit}
    />
  );
};

export default BatchAssignModal;
