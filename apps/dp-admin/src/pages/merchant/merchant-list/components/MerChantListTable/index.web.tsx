import { Button, Image, Space, Table, Tag } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React from 'react';

import type { MerchantData, MerchantTableProps } from '../../types';

/**
 * 商户列表表格组件
 *
 * 显示商户列表数据，支持分页、选择和操作
 */
const MerchantListTable: React.FC<MerchantTableProps> = ({
  loading,
  data,
  selectedRowKeys,
  currentPage,
  pageSize,
  total,
  onPageChange,
  onRowSelectionChange,
  onMerchantDetail,
  onMerchantDecoration,
  onMenuManagement,
  onRelatedTasks,
  onChangeAssignment,
  onAccountManagement,
}) => {
  // 获取商户状态标签颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return 'green';
      case 'PENDING':
        return 'orange';
      case 'REJECTED':
        return 'red';
      case 'DISABLED':
        return 'gray';
      default:
        return 'default';
    }
  };

  // 获取营业状态标签颜色
  const getBusinessStatusColor = (status: string) => {
    switch (status) {
      case 'OPEN':
        return 'green';
      case 'CLOSED':
        return 'orange';
      case 'PERMANENTLY_CLOSED':
        return 'red';
      default:
        return 'default';
    }
  };

  // 获取商户状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return '已通过';
      case 'PENDING':
        return '待审核';
      case 'REJECTED':
        return '已驳回';
      case 'DISABLED':
        return '已停用';
      default:
        return status;
    }
  };

  // 获取营业状态文本
  const getBusinessStatusText = (status: string) => {
    switch (status) {
      case 'OPEN':
        return '营业中';
      case 'CLOSED':
        return '暂停营业';
      case 'PERMANENTLY_CLOSED':
        return '永久关闭';
      default:
        return status;
    }
  };

  // 定义表格列
  const columns: ColumnsType<MerchantData> = [
    {
      title: '商戶ID',
      dataIndex: 'merchantId',
      key: 'merchantId',
      width: 120,
      fixed: 'left',
    },
    {
      title: '商戶狀態',
      dataIndex: 'merchantStatus',
      key: 'merchantStatus',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>{getStatusText(status)}</Tag>
      ),
    },
    {
      title: '商戶類目',
      dataIndex: 'category',
      key: 'category',
      width: 100,
    },
    {
      title: '商戶圖片',
      dataIndex: 'imageUrl',
      key: 'imageUrl',
      width: 80,
      render: (imageUrl: string) =>
        imageUrl ? (
          <Image
            src={imageUrl}
            alt="商户图片"
            width={50}
            height={50}
            style={{ objectFit: 'cover' }}
          />
        ) : (
          <div className="w-12 h-12 bg-gray-200 rounded flex items-center justify-center">
            <span className="text-gray-400 text-xs">无图片</span>
          </div>
        ),
    },
    {
      title: '城市',
      dataIndex: 'city',
      key: 'city',
      width: 80,
    },
    {
      title: '行政區',
      dataIndex: 'district',
      key: 'district',
      width: 80,
    },
    {
      title: '商圈',
      dataIndex: 'businessArea',
      key: 'businessArea',
      width: 100,
    },
    {
      title: '商戶名稱',
      dataIndex: 'merchantName',
      key: 'merchantName',
      width: 150,
    },
    {
      title: '商戶地址',
      dataIndex: 'address',
      key: 'address',
      width: 200,
      ellipsis: true,
    },
    {
      title: '營業狀態',
      dataIndex: 'businessStatus',
      key: 'businessStatus',
      width: 100,
      render: (status: string) => (
        <Tag color={getBusinessStatusColor(status)}>
          {getBusinessStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '品牌',
      dataIndex: 'brand',
      key: 'brand',
      width: 100,
    },
    {
      title: '商戶等級',
      dataIndex: 'level',
      key: 'level',
      width: 80,
      render: (level: string) => (
        <Tag
          color={
            level === 'A'
              ? 'gold'
              : level === 'B'
                ? 'blue'
                : level === 'C'
                  ? 'green'
                  : 'default'
          }
        >
          {level}級
        </Tag>
      ),
    },
    {
      title: '統一編碼',
      dataIndex: 'unifiedCode',
      key: 'unifiedCode',
      width: 120,
    },
    {
      title: '加盟類型',
      dataIndex: 'franchiseType',
      key: 'franchiseType',
      width: 100,
      render: (type: string) => {
        const typeMap = {
          DIRECT: '直营',
          FRANCHISE: '加盟',
          COOPERATION: '合作',
        };
        return typeMap[type as keyof typeof typeMap] || type;
      },
    },
    {
      title: 'BD',
      dataIndex: 'bd',
      key: 'bd',
      width: 80,
    },
    {
      title: '更新時間',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 150,
    },
    {
      title: '入住時間',
      dataIndex: 'joinedAt',
      key: 'joinedAt',
      width: 150,
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 400,
      render: (_, record) => (
        <Space size="small" wrap>
          <Button
            type="link"
            size="small"
            onClick={() => onMerchantDetail(record.id)}
          >
            商戶詳情
          </Button>
          <Button
            type="link"
            size="small"
            onClick={() => onMerchantDecoration(record.id)}
          >
            商戶裝修
          </Button>
          <Button
            type="link"
            size="small"
            onClick={() => onMenuManagement(record.id)}
          >
            菜單&菜品
          </Button>
          <Button
            type="link"
            size="small"
            onClick={() => onRelatedTasks(record.id)}
          >
            關聯任務
          </Button>
          <Button
            type="link"
            size="small"
            onClick={() => onChangeAssignment(record.id)}
          >
            更改分配
          </Button>
          <Button
            type="link"
            size="small"
            onClick={() => onAccountManagement(record.id)}
          >
            帳號管理
          </Button>
        </Space>
      ),
    },
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys: React.Key[], selectedRows: MerchantData[]) => {
      onRowSelectionChange(
        selectedRowKeys as (string | number)[],
        selectedRows
      );
    },
    getCheckboxProps: (record: MerchantData) => ({
      disabled: false,
      name: record.id.toString(),
    }),
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
      <Table
        columns={columns}
        dataSource={data}
        rowKey="id"
        loading={loading}
        rowSelection={rowSelection}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          total: total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total: number, range: [number, number]) =>
            `第 ${range[0]}-${range[1]} 條，共 ${total} 條`,
          pageSizeOptions: ['10', '20', '50', '100'],
          onChange: onPageChange,
          onShowSizeChange: onPageChange,
          style: { margin: '16px' },
        }}
        scroll={{ x: 1800 }}
        size="middle"
        className="w-full"
        rowClassName="hover:bg-gray-50"
      />
    </div>
  );
};

export default MerchantListTable;
