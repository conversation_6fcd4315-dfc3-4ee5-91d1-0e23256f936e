import { EyeOutlined, MoreOutlined } from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { Button, Dropdown, Space } from 'antd';
import React from 'react';

import type { MerchantActionButtonsProps } from '../../types';

/**
 * 商户操作按钮组件
 *
 * 提供商户的查看详情和更多操作功能
 */
const ActionButtons: React.FC<MerchantActionButtonsProps> = ({
  record,
  onMerchantDetail,
  onMerchantDecoration,
  onMenuManagement,
  onRelatedTasks,
  onChangeAssignment,
  onAccountManagement,
}) => {
  // 下拉菜单项
  const menuItems: MenuProps['items'] = [
    {
      key: 'merchantDecoration',
      label: '商戶裝修',
      onClick: () => onMerchantDecoration(record.id),
      style: { color: '#eb984e' },
    },
    {
      key: 'menuManagement',
      label: '菜單&菜品',
      onClick: () => onMenuManagement(record.id),
      style: { color: '#52be80' },
    },
    {
      key: 'relatedTasks',
      label: '關聯任務',
      onClick: () => onRelatedTasks(record.id),
      style: { color: '#9b59b6' },
    },
    {
      key: 'changeAssignment',
      label: '更改分配',
      onClick: () => onChangeAssignment(record.id),
      style: { color: '#3498db' },
    },
    {
      key: 'accountManagement',
      label: '帳號管理',
      onClick: () => onAccountManagement(record.id),
      style: { color: '#e74c3c' },
    },
  ];

  return (
    <Space.Compact>
      {/* 商户详情按钮 */}
      <Button
        type="link"
        size="small"
        icon={<EyeOutlined />}
        onClick={() => onMerchantDetail(record.id)}
        className="text-blue-600 hover:text-blue-800"
        title="商戶詳情"
      >
        商戶詳情
      </Button>

      {/* 更多操作下拉菜单 */}
      <Dropdown
        menu={{ items: menuItems }}
        trigger={['click']}
        placement="bottomRight"
      >
        <Button
          type="text"
          size="small"
          icon={<MoreOutlined />}
          className="text-gray-600 hover:text-gray-800"
          title="更多操作"
        />
      </Dropdown>
    </Space.Compact>
  );
};

export default ActionButtons;
