import { Tag } from 'antd';
import React from 'react';

/**
 * 商户等级配置映射
 */
const MERCHANT_LEVEL_CONFIG = {
  A: {
    label: 'A級',
    color: 'gold',
  },
  B: {
    label: 'B級',
    color: 'blue',
  },
  C: {
    label: 'C級',
    color: 'green',
  },
  D: {
    label: 'D級',
    color: 'orange',
  },
  E: {
    label: 'E級',
    color: 'red',
  },
} as const;

/**
 * 商户等级标签组件属性接口
 */
export interface MerchantLevelTagProps {
  /** 商户等级 */
  level: string;
  /** 自定义样式类名 */
  className?: string;
}

/**
 * 获取商户等级配置
 * @param level 商户等级
 * @returns 等级配置对象
 */
export const getMerchantLevelConfig = (level: string) => {
  return MERCHANT_LEVEL_CONFIG[level as keyof typeof MERCHANT_LEVEL_CONFIG] || {
    label: `${level}級`,
    color: 'default',
  };
};

/**
 * 商户等级标签组件
 *
 * 根据商户等级显示对应的标签样式
 */
const MerchantLevelTag: React.FC<MerchantLevelTagProps> = ({
  level,
  className,
}) => {
  const config = getMerchantLevelConfig(level);

  return (
    <Tag color={config.color} className={className}>
      {config.label}
    </Tag>
  );
};

export default MerchantLevelTag;
