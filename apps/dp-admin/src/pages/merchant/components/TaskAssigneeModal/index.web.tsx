/* eslint-disable @typescript-eslint/no-explicit-any */
import { Button, Flex, Form, Input, Modal, Select } from 'antd';
import React, { useEffect } from 'react';

import CommTable from '../CommTable/index.web';

/**
 * 分配人选项接口
 */
export interface AssigneeOption {
  /** 用户ID */
  id: string;
  /** 用户姓名 */
  name: string;
  /** 用户邮箱 */
  email: string;
  /** 是否为当前用户 */
  isCurrentUser?: boolean;
  /** 部门（可选） */
  department?: string;
  /** 职位（可选） */
  position?: string;
}

/**
 * 分配记录接口
 */
export interface AssignRecord {
  /** 记录ID */
  id: string | number;
  /** 分配时间 */
  assignTime: string;
  /** 新跟进人 */
  newAssignee: string;
  /** 分配人 */
  assignedBy: string;
  /** 备注（可选） */
  remark?: string;
}

/**
 * 任务分配表单数据接口
 */
export interface TaskAssignFormData {
  /** 新分配人ID */
  assigneeId: string;
  /** 备注（可选） */
  remark?: string;
}

/**
 * 任务分配模态框属性接口
 */
export interface TaskAssigneeModalProps {
  /** 是否可见 */
  visible: boolean;
  /** 确认加载状态（可选） */
  confirmLoading?: boolean;
  /** 任务ID（可选） */
  taskId?: string | number;
  /** 任务数据（可选） */
  taskData?: any;
  /** 当前跟进人 */
  currentAssignee?: string;
  /** 分配人选项列表 */
  assigneeOptions: AssigneeOption[];
  /** 分配历史列表 */
  assignHistory: AssignRecord[];
  /** 取消回调 */
  onCancel: () => void;
  /** 保存回调 */
  onSave: (data: TaskAssignFormData) => void;
}

/**
 * 任务分配彈窗組件
 *
 * 提供任务分配功能，包括选择新跟进人和查看分配历史
 */
const TaskAssigneeModal: React.FC<TaskAssigneeModalProps> = ({
  visible,
  confirmLoading = false,
  taskId: _taskId,
  taskData,
  currentAssignee,
  assigneeOptions,
  assignHistory,
  onCancel,
  onSave,
}) => {
  const [form] = Form.useForm();

  // 当模态框显示时重置表单
  useEffect(() => {
    if (visible) {
      form.resetFields();
    }
  }, [visible, form]);

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      onSave(values);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 处理取消
  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  // 渲染分配人选项
  const renderAssigneeOption = (option: AssigneeOption) => {
    const prefix = option.isCurrentUser ? '(我) ' : '';
    const emailPrefix = option.email.split('@')[0];
    return `${prefix}${option.name}+${emailPrefix}`;
  };

  // 分配历史表格列定义
  const assignHistoryColumns = [
    {
      title: '分配時間',
      dataIndex: 'assignTime',
      key: 'assignTime',
      width: 150,
    },
    {
      title: '新跟進人',
      dataIndex: 'newAssignee',
      key: 'newAssignee',
      width: 120,
    },
    {
      title: '分配人',
      dataIndex: 'assignedBy',
      key: 'assignedBy',
      width: 120,
    },
    {
      title: '備註',
      dataIndex: 'remark',
      key: 'remark',
      render: (text: string) => text || '--',
    },
  ];

  return (
    <Modal
      open={visible}
      title="分配任務"
      width={800}
      confirmLoading={confirmLoading}
      destroyOnHidden
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={confirmLoading}
          onClick={handleSubmit}
        >
          確認分配
        </Button>,
      ]}
    >
      <div className="space-y-6">
        {/* 任務基本信息 */}
        {taskData && (
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="text-sm font-medium text-gray-900 mb-2">任務信息</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">任務名稱：</span>
                <span className="text-gray-900">{taskData.taskName}</span>
              </div>
              <div>
                <span className="text-gray-600">商戶名稱：</span>
                <span className="text-gray-900">{taskData.merchantName}</span>
              </div>
            </div>
          </div>
        )}

        {/* 分配表单 */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">分配設置</h4>
          <Form form={form} layout="vertical">
            <div className="mb-4">
              <Flex gap="middle" align="center">
                <span className="text-gray-600 min-w-[80px]">當前跟進人:</span>
                <span className="text-gray-900">
                  {currentAssignee || (
                    <span className="text-gray-400">未分配</span>
                  )}
                </span>
              </Flex>
            </div>

            <Form.Item
              name="assigneeId"
              label="新跟進人"
              rules={[{ required: true, message: '請選擇新跟進人' }]}
            >
              <Select
                showSearch
                placeholder="請選擇新跟進人"
                optionLabelProp="label"
                filterOption={(input, option) =>
                  ((option?.label ?? '') as string)
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
              >
                {assigneeOptions.map((option) => {
                  const label = renderAssigneeOption(option);
                  return (
                    <Select.Option
                      key={option.id}
                      value={option.id}
                      label={label}
                    >
                      {label}
                    </Select.Option>
                  );
                })}
              </Select>
            </Form.Item>

            <Form.Item name="remark" label="備註">
              <Input.TextArea
                placeholder="請輸入分配備註（可選）"
                rows={3}
                maxLength={200}
                showCount
              />
            </Form.Item>
          </Form>
        </div>

        {/* 分配歷史 */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">分配歷史</h4>
          <CommTable
            dataSource={assignHistory}
            columns={assignHistoryColumns}
            pageSize={5}
            current={1}
            total={assignHistory.length}
          />
        </div>
      </div>
    </Modal>
  );
};

export default TaskAssigneeModal;
