/* eslint-disable @typescript-eslint/no-explicit-any */
import { Table } from 'antd';
import type { PropsWithChildren } from 'react';
import React from 'react';

export interface ICommTable {
  dataSource: Record<string, any>[];
  columns: Record<string, any>[];
  loading?: boolean;
  pageSize?: number;
  current?: number;
  total?: number;
  onChange?: () => void;
  onShowSizeChange?: () => void;
  rowKey?: string;
}

type CommTableProps = PropsWithChildren<ICommTable & Record<string, any>>;

/**
 * 通用表格组件
 *
 * 提供统一的表格样式和分页配置
 */
export default function CommTable({
  dataSource,
  columns,
  loading,
  pageSize,
  current,
  total,
  onChange,
  onShowSizeChange,
  rowKey,
  ...props
}: CommTableProps) {
  return (
    <Table
      dataSource={dataSource}
      columns={columns}
      rowKey={rowKey || 'id'}
      loading={loading}
      bordered
      size="small"
      pagination={{
        current: current,
        pageSize: pageSize,
        total: total,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) =>
          `第 ${range[0]}-${range[1]} 條，共 ${total} 條`,
        pageSizeOptions: ['10', '20', '50', '100'],
        onChange: onChange,
        onShowSizeChange: onShowSizeChange,
        style: { margin: '16px' },
      }}
      scroll={{ x: 600 }}
      locale={{
        emptyText: '暫無數據',
      }}
      className="w-full"
      {...props}
    />
  );
}
