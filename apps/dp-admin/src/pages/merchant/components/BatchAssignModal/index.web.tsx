/* eslint-disable @typescript-eslint/no-explicit-any */
import { Form, Modal, Select, Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React, { useEffect } from 'react';

const { Option } = Select;

/**
 * 批量分配选项接口
 */
export interface AssigneeOption {
  /** 用户ID */
  id: string;
  /** 用户姓名 */
  name: string;
  /** 用户邮箱 */
  email: string;
  /** 是否为当前用户 */
  isCurrentUser?: boolean;
  /** 部门（可选） */
  department?: string;
  /** 职位（可选） */
  position?: string;
}

/**
 * 批量分配表单数据接口
 */
export interface BatchAssignFormData {
  /** 分配人ID */
  assigneeId: string;
  /** 分配人姓名 */
  assigneeName: string;
  /** 项目ID列表 */
  itemIds: (string | number)[];
}

/**
 * 批量分配模态框属性接口
 */
export interface BatchAssignModalProps {
  /** 是否可见 */
  visible: boolean;
  /** 确认加载状态 */
  confirmLoading: boolean;
  /** 选中的项目列表 */
  selectedItems: any[];
  /** 分配人选项列表 */
  assigneeOptions: AssigneeOption[];
  /** 模态框标题 */
  title?: string;
  /** 分配人标签 */
  assigneeLabel?: string;
  /** 项目类型名称（如：任务、商户等） */
  itemTypeName?: string;
  /** 表格列配置 */
  tableColumns?: ColumnsType<any>;
  /** 取消回调 */
  onCancel: () => void;
  /** 提交回调 */
  onSubmit: (data: BatchAssignFormData) => void;
}

/**
 * 通用批量分配模态框组件
 *
 * 用于批量分配任务、商户等给指定的跟进人
 */
const BatchAssignModal: React.FC<BatchAssignModalProps> = ({
  visible,
  confirmLoading,
  selectedItems,
  assigneeOptions,
  title = '批量分配',
  assigneeLabel = '選擇跟進人',
  itemTypeName = '項目',
  tableColumns,
  onCancel,
  onSubmit,
}) => {
  const [form] = Form.useForm();

  // 当模态框显示时重置表单
  useEffect(() => {
    if (visible) {
      form.resetFields();
    }
  }, [visible, form]);

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      const assignee = assigneeOptions.find((a) => a.id === values.assigneeId);

      onSubmit({
        assigneeId: values.assigneeId,
        assigneeName: assignee?.name || '',
        itemIds: selectedItems.map((item) => item.id),
      });
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 处理取消
  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  // 渲染分配人选项
  const renderAssigneeOption = (option: AssigneeOption) => {
    const prefix = option.isCurrentUser ? '(我) ' : '';
    const emailPrefix = option.email.split('@')[0];
    return `${prefix}${option.name}+${emailPrefix}`;
  };

  // 默认表格列定义
  const defaultColumns: ColumnsType<any> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 100,
    },
    {
      title: '名稱',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (text: string) => (
        <span className="line-clamp-1" title={text}>
          {text}
        </span>
      ),
    },
    {
      title: '當前跟進人',
      dataIndex: 'assignee',
      key: 'assignee',
      width: 120,
      render: (text: string) => (
        <span className="text-gray-600">
          {text || <span className="text-gray-400">未分配</span>}
        </span>
      ),
    },
  ];

  const columns = tableColumns || defaultColumns;

  return (
    <Modal
      title={title}
      open={visible}
      onOk={handleSubmit}
      onCancel={handleCancel}
      confirmLoading={confirmLoading}
      okText="確認分配"
      cancelText="取消"
      width={800}
      destroyOnHidden
      className="batch-assign-modal"
    >
      <div className="space-y-4">
        {/* 分配表单 */}
        <Form form={form} layout="horizontal" className="mb-4">
          <Form.Item
            label={assigneeLabel}
            name="assigneeId"
            rules={[
              {
                required: true,
                message: `請選擇${assigneeLabel.replace('選擇', '')}`,
              },
            ]}
          >
            <Select
              placeholder={`請選擇要分配的${assigneeLabel.replace('選擇', '')}`}
              className="w-full"
              optionLabelProp="label"
            >
              {assigneeOptions.map((option) => {
                const label = renderAssigneeOption(option);
                return (
                  <Option key={option.id} value={option.id} label={label}>
                    {label}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
        </Form>

        {/* 选中的项目列表 */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-2">
            已選中的{itemTypeName} ({selectedItems.length} 個)
          </h4>
          <Table
            columns={columns}
            dataSource={selectedItems}
            rowKey="id"
            pagination={false}
            size="small"
            scroll={{ y: 300 }}
            className="border border-gray-200 rounded"
          />
        </div>

        {/* 提示信息 */}
        <div className="bg-blue-50 border border-blue-200 rounded p-3">
          <p className="text-sm text-blue-800">
            <strong>注意：</strong>
            批量分配將會覆蓋所選{itemTypeName}的當前跟進人，請確認後再進行操作。
          </p>
        </div>
      </div>
    </Modal>
  );
};

export default BatchAssignModal;
