import { PlusOutlined } from '@ant-design/icons';
import { Button, Input, Tag } from 'antd';
import React, { useState } from 'react';

/**
 * 商户标签组件属性接口
 */
export interface MerchantTagsProps {
  /** 标签列表 */
  tags?: string[];
  /** 是否为编辑模式 */
  isEditing?: boolean;
  /** 添加标签回调 */
  onAddTag?: (tag: string) => void;
  /** 删除标签回调 */
  onRemoveTag?: (tag: string) => void;
  /** 自定义类名 */
  className?: string;
}

/**
 * 商户标签管理组件
 *
 * 支持查看和编辑模式，在编辑模式下可以添加和删除标签
 */
const MerchantTags: React.FC<MerchantTagsProps> = ({
  tags = [],
  isEditing = false,
  onAddTag,
  onRemoveTag,
  className = '',
}) => {
  const [newTag, setNewTag] = useState('');

  // 添加标签
  const handleAddTag = () => {
    if (newTag.trim() && onAddTag) {
      onAddTag(newTag.trim());
      setNewTag('');
    }
  };

  // 处理回车键添加标签
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleAddTag();
    }
  };

  return (
    <div className={`space-y-2 ${className}`}>
      <div className="flex items-center justify-between">
        <label className="text-sm font-medium text-gray-700">商戶標籤</label>
        {isEditing && (
          <span className="text-xs text-gray-500">{tags.length} 個標籤</span>
        )}
      </div>

      {/* 标签显示区域 */}
      <div className="flex flex-wrap gap-1 sm:gap-2 min-h-[32px] p-2 border border-gray-200 rounded-md bg-gray-50">
        {tags.length > 0 ? (
          tags.map((tag, index) => (
            <Tag
              key={index}
              closable={isEditing}
              onClose={() => onRemoveTag?.(tag)}
              className="text-xs sm:text-sm m-0"
              color="blue"
            >
              {tag}
            </Tag>
          ))
        ) : (
          <span className="text-xs text-gray-400 py-1">
            {isEditing ? '暫無標籤，請添加' : '暫無標籤'}
          </span>
        )}
      </div>

      {/* 编辑模式下的添加标签区域 */}
      {isEditing && (
        <div className="flex gap-2">
          <Input
            size="small"
            placeholder="輸入新標籤名稱"
            value={newTag}
            onChange={(e) => setNewTag(e.target.value)}
            onKeyPress={handleKeyPress}
            className="flex-1 text-sm"
            maxLength={20}
          />
          <Button
            size="small"
            type="dashed"
            icon={<PlusOutlined />}
            onClick={handleAddTag}
            disabled={!newTag.trim()}
            className="flex-shrink-0"
          >
            新增
          </Button>
        </div>
      )}

      {/* 编辑模式下的提示信息 */}
      {isEditing && (
        <div className="text-xs text-gray-500">
          提示：標籤用於分類和篩選商戶，建議使用簡潔明確的詞語
        </div>
      )}
    </div>
  );
};

export default MerchantTags;
