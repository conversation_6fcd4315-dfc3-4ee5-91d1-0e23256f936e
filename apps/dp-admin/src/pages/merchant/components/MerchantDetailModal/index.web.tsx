import { Button, Form, Image, message, Modal, Tag } from 'antd';
import React, { useEffect, useState } from 'react';

import MerchantEditForm from './MerchantEditForm.web';
import MerchantInfo from './MerchantInfo.web';
import MerchantTags from './MerchantTags.web';

/**
 * 商户详情数据接口
 */
export interface MerchantDetailData {
  /** 商户ID */
  id: string | number;
  /** 商户名称 */
  merchantName: string;
  /** 商户地址 */
  address: string;
  /** 商户图片 */
  imageUrl?: string;
  /** 商户等级 */
  level: string;
  /** 正常营业 */
  businessStatus: string;
  /** BD */
  bd: string;
  /** 商户电话1 */
  phone1?: string;
  /** 商户电话2 */
  phone2?: string;
  /** 城市 */
  city?: string;
  /** 行政区 */
  district?: string;
  /** 商圈 */
  businessArea?: string;
  /** 品牌 */
  brand?: string;
  /** 统一编码 */
  unifiedCode?: string;
  /** 加盟类型 */
  franchiseType?: string;
  /** 商户状态 */
  merchantStatus?: string;
  /** 营业时间 */
  businessHours?: string;
  /** 创建时间 */
  createdAt?: string;
  /** 更新时间 */
  updatedAt?: string;
  /** 商户类目 */
  category?: string;
  /** 标签列表 */
  tags?: string[];
  /** 详细地址 */
  detailAddress?: string;
}

/**
 * 商户详情弹窗组件属性接口
 */
export interface MerchantDetailModalProps {
  /** 是否可见 */
  visible: boolean;
  /** 商户详情数据 */
  merchantData?: MerchantDetailData;
  /** 加载状态 */
  loading?: boolean;
  /** 取消回调 */
  onCancel: () => void;

  /** 商户装修回调 */
  onMerchantDecoration?: (merchantId: string | number) => void;
  /** 菜单管理回调 */
  onMenuManagement?: (merchantId: string | number) => void;
  /** 保存编辑回调 */
  onSave?: (merchantData: MerchantDetailData) => Promise<void>;
}

/**
 * 商户详情弹窗组件
 *
 * 显示商户的详细信息，包括基本信息、联系方式、状态等
 */
const MerchantDetailModal: React.FC<MerchantDetailModalProps> = ({
  visible,
  merchantData,
  onCancel,
  onMerchantDecoration,
  onMenuManagement,
  onSave,
}) => {
  const [form] = Form.useForm();
  const [isEditing, setIsEditing] = useState(false);
  const [editingData, setEditingData] = useState<
    MerchantDetailData | undefined
  >(undefined);
  const [saving, setSaving] = useState(false);
  // 获取商户等级配置
  const getLevelConfig = (level: string) => {
    const configs = {
      A: { color: 'gold', text: 'A級' },
      B: { color: 'blue', text: 'B級' },
      C: { color: 'green', text: 'C級' },
      D: { color: 'orange', text: 'D級' },
      E: { color: 'red', text: 'E級' },
    };
    return (
      configs[level as keyof typeof configs] || {
        color: 'default',
        text: `${level}級`,
      }
    );
  };

  // 获取营业状态配置
  const getBusinessStatusConfig = (status: string) => {
    const configs = {
      OPEN: { color: 'green', text: '營業中' },
      CLOSED: { color: 'orange', text: '暫停營業' },
      PERMANENTLY_CLOSED: { color: 'red', text: '永久關閉' },
    };
    return (
      configs[status as keyof typeof configs] || {
        color: 'default',
        text: status,
      }
    );
  };

  // 获取商户状态配置
  const getMerchantStatusConfig = (status: string) => {
    const configs = {
      APPROVED: { color: 'green', text: '已通過' },
      PENDING: { color: 'orange', text: '待審核' },
      REJECTED: { color: 'red', text: '已駁回' },
      DISABLED: { color: 'gray', text: '已停用' },
    };
    return (
      configs[status as keyof typeof configs] || {
        color: 'default',
        text: status,
      }
    );
  };

  // 初始化编辑数据
  useEffect(() => {
    if (merchantData && isEditing) {
      setEditingData({ ...merchantData });
      form.setFieldsValue({
        merchantName: merchantData.merchantName,
        address: merchantData.address,
        businessStatus: merchantData.businessStatus,
        merchantStatus: merchantData.merchantStatus,
        phone1: merchantData.phone1,
        phone2: merchantData.phone2,
        category: merchantData.category,
      });
    }
  }, [merchantData, isEditing, form]);

  // 开始编辑
  const handleStartEdit = () => {
    if (merchantData) {
      setIsEditing(true);
      setEditingData({ ...merchantData });
    }
  };

  // 取消编辑
  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditingData(undefined);
    form.resetFields();
  };

  // 保存编辑
  const handleSaveEdit = async () => {
    try {
      setSaving(true);
      const values = await form.validateFields();
      const updatedData = {
        ...editingData!,
        ...values,
        tags: editingData?.tags || [],
      };

      if (onSave) {
        await onSave(updatedData);
        message.success('保存成功');
        setIsEditing(false);
        setEditingData(undefined);
      }
    } catch {
      message.error('保存失败，请检查输入信息');
    } finally {
      setSaving(false);
    }
  };

  // 添加标签
  const handleAddTag = (tag: string) => {
    if (tag.trim() && editingData) {
      const updatedTags = [...(editingData.tags || []), tag.trim()];
      setEditingData({ ...editingData, tags: updatedTags });
    }
  };

  // 删除标签
  const handleRemoveTag = (tagToRemove: string) => {
    if (editingData) {
      const updatedTags =
        editingData.tags?.filter((tag) => tag !== tagToRemove) || [];
      setEditingData({ ...editingData, tags: updatedTags });
    }
  };

  // 处理编辑数据变化
  const handleEditingDataChange = (data: MerchantDetailData) => {
    setEditingData(data);
  };

  if (!merchantData) {
    return null;
  }

  const currentData = isEditing ? editingData : merchantData;
  const levelConfig = getLevelConfig(currentData?.level || '');
  const businessStatusConfig = getBusinessStatusConfig(
    currentData?.businessStatus || ''
  );
  const merchantStatusConfig = currentData?.merchantStatus
    ? getMerchantStatusConfig(currentData.merchantStatus)
    : null;

  return (
    <Modal
      title="商戶詳情"
      open={visible}
      onCancel={onCancel}
      width="90vw"
      style={{ maxWidth: '800px' }}
      footer={
        isEditing
          ? [
              <Button
                key="cancel-edit"
                onClick={handleCancelEdit}
                className="w-full sm:w-auto"
              >
                取消
              </Button>,
              <Button
                key="save"
                type="primary"
                loading={saving}
                onClick={handleSaveEdit}
                className="w-full sm:w-auto mt-2 sm:mt-0"
              >
                保存
              </Button>,
            ]
          : [
              <Button
                key="cancel"
                onClick={onCancel}
                className="w-full sm:w-auto"
              >
                關閉
              </Button>,
              <Button
                key="edit"
                type="primary"
                onClick={handleStartEdit}
                className="w-full sm:w-auto mt-2 sm:mt-0"
              >
                編輯
              </Button>,
            ]
      }
      className="merchant-detail-modal"
      centered
    >
      <div className="space-y-4 sm:space-y-6">
        {/* 商户基本信息 */}
        <div className="flex flex-col sm:flex-row items-start space-y-4 sm:space-y-0 sm:space-x-4 mb-0">
          {/* 商户图片 */}
          <div className="flex-shrink-0 w-full sm:w-auto flex justify-center sm:justify-start">
            {currentData?.imageUrl ? (
              <Image
                src={currentData.imageUrl}
                alt="商户图片"
                width={120}
                height={120}
                className="rounded-lg object-cover w-24 h-24 sm:w-30 sm:h-30"
                placeholder={
                  <div className="w-24 h-24 sm:w-30 sm:h-30 bg-gray-200 rounded-lg flex items-center justify-center">
                    <span className="text-gray-400 text-xs sm:text-sm">
                      圖片
                    </span>
                  </div>
                }
              />
            ) : (
              <div className="w-24 h-24 sm:w-30 sm:h-30 bg-gray-200 rounded-lg flex items-center justify-center">
                <span className="text-gray-400 text-xs sm:text-sm">無圖片</span>
              </div>
            )}
          </div>

          {/* 基本信息 */}
          <div className="flex-1 space-y-2 w-full">
            {isEditing ? (
              <MerchantEditForm
                form={form}
                editingData={editingData}
                onDataChange={handleEditingDataChange}
                onAddTag={handleAddTag}
                onRemoveTag={handleRemoveTag}
              />
            ) : (
              <>
                <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-3">
                  <h3 className="text-base sm:text-lg font-medium text-gray-900 break-words">
                    {currentData?.merchantName}
                  </h3>
                  <div className="flex flex-wrap gap-1 sm:gap-2">
                    <Tag color={levelConfig.color} className="text-xs">
                      {levelConfig.text}
                    </Tag>
                    <Tag color={businessStatusConfig.color} className="text-xs">
                      {businessStatusConfig.text}
                    </Tag>
                    {merchantStatusConfig && (
                      <Tag
                        color={merchantStatusConfig.color}
                        className="text-xs"
                      >
                        {merchantStatusConfig.text}
                      </Tag>
                    )}
                  </div>
                </div>
                <p className="text-sm sm:text-base text-gray-600 break-words">
                  {currentData?.address}
                </p>
                <div className="text-xs sm:text-sm text-gray-500">
                  BD: {currentData?.bd}
                </div>
              </>
            )}
          </div>
        </div>

        {/* 详细信息 - 只在非编辑模式显示 */}
        {!isEditing && (
          <>
            {/* 商户标签展示 - 始终显示 */}
            <MerchantTags
              tags={currentData?.tags}
              isEditing={false}
              className="border-t border-t-[#dfdfdf] pt-3"
            />
            <MerchantInfo merchantData={merchantData} />
          </>
        )}
      </div>
    </Modal>
  );
};

export default MerchantDetailModal;
