import { Button, Descriptions, Image, Modal, Tag } from 'antd';
import React from 'react';

/**
 * 商户详情数据接口
 */
export interface MerchantDetailData {
  /** 商户ID */
  id: string | number;
  /** 商户名称 */
  merchantName: string;
  /** 商户地址 */
  address: string;
  /** 商户图片 */
  imageUrl?: string;
  /** 商户等级 */
  level: string;
  /** 正常营业 */
  businessStatus: string;
  /** BD */
  bd: string;
  /** 商户电话1 */
  phone1?: string;
  /** 商户电话2 */
  phone2?: string;
  /** 城市 */
  city?: string;
  /** 行政区 */
  district?: string;
  /** 商圈 */
  businessArea?: string;
  /** 品牌 */
  brand?: string;
  /** 统一编码 */
  unifiedCode?: string;
  /** 加盟类型 */
  franchiseType?: string;
  /** 商户状态 */
  merchantStatus?: string;
  /** 营业时间 */
  businessHours?: string;
  /** 创建时间 */
  createdAt?: string;
  /** 更新时间 */
  updatedAt?: string;
}

/**
 * 商户详情弹窗组件属性接口
 */
export interface MerchantDetailModalProps {
  /** 是否可见 */
  visible: boolean;
  /** 商户详情数据 */
  merchantData?: MerchantDetailData;
  /** 加载状态 */
  loading?: boolean;
  /** 取消回调 */
  onCancel: () => void;
  /** 编辑回调 */
  onEdit?: (merchantId: string | number) => void;
  /** 商户装修回调 */
  onMerchantDecoration?: (merchantId: string | number) => void;
  /** 菜单管理回调 */
  onMenuManagement?: (merchantId: string | number) => void;
}

/**
 * 商户详情弹窗组件
 *
 * 显示商户的详细信息，包括基本信息、联系方式、状态等
 */
const MerchantDetailModal: React.FC<MerchantDetailModalProps> = ({
  visible,
  merchantData,
  onCancel,
  onEdit,
  onMerchantDecoration,
  onMenuManagement,
}) => {
  // 获取商户等级配置
  const getLevelConfig = (level: string) => {
    const configs = {
      A: { color: 'gold', text: 'A級' },
      B: { color: 'blue', text: 'B級' },
      C: { color: 'green', text: 'C級' },
      D: { color: 'orange', text: 'D級' },
      E: { color: 'red', text: 'E級' },
    };
    return (
      configs[level as keyof typeof configs] || {
        color: 'default',
        text: `${level}級`,
      }
    );
  };

  // 获取营业状态配置
  const getBusinessStatusConfig = (status: string) => {
    const configs = {
      OPEN: { color: 'green', text: '營業中' },
      CLOSED: { color: 'orange', text: '暫停營業' },
      PERMANENTLY_CLOSED: { color: 'red', text: '永久關閉' },
    };
    return (
      configs[status as keyof typeof configs] || {
        color: 'default',
        text: status,
      }
    );
  };

  // 获取商户状态配置
  const getMerchantStatusConfig = (status: string) => {
    const configs = {
      APPROVED: { color: 'green', text: '已通過' },
      PENDING: { color: 'orange', text: '待審核' },
      REJECTED: { color: 'red', text: '已駁回' },
      DISABLED: { color: 'gray', text: '已停用' },
    };
    return (
      configs[status as keyof typeof configs] || {
        color: 'default',
        text: status,
      }
    );
  };

  // 获取加盟类型文本
  const getFranchiseTypeText = (type: string) => {
    const types = {
      DIRECT: '直營',
      FRANCHISE: '加盟',
      COOPERATION: '合作',
    };
    return types[type as keyof typeof types] || type;
  };

  if (!merchantData) {
    return null;
  }

  const levelConfig = getLevelConfig(merchantData.level);
  const businessStatusConfig = getBusinessStatusConfig(
    merchantData.businessStatus
  );
  const merchantStatusConfig = merchantData.merchantStatus
    ? getMerchantStatusConfig(merchantData.merchantStatus)
    : null;

  return (
    <Modal
      title="商戶詳情"
      open={visible}
      onCancel={onCancel}
      width="90vw"
      style={{ maxWidth: '800px' }}
      footer={[
        <Button key="cancel" onClick={onCancel} className="w-full sm:w-auto">
          關閉
        </Button>,
        <Button
          key="edit"
          type="primary"
          onClick={() => onEdit?.(merchantData.id)}
          className="w-full sm:w-auto mt-2 sm:mt-0"
        >
          編輯
        </Button>,
      ]}
      className="merchant-detail-modal"
      centered
    >
      <div className="space-y-4 sm:space-y-6">
        {/* 商户基本信息 */}
        <div className="flex flex-col sm:flex-row items-start space-y-4 sm:space-y-0 sm:space-x-4">
          {/* 商户图片 */}
          <div className="flex-shrink-0 w-full sm:w-auto flex justify-center sm:justify-start">
            {merchantData.imageUrl ? (
              <Image
                src={merchantData.imageUrl}
                alt="商户图片"
                width={120}
                height={120}
                className="rounded-lg object-cover w-24 h-24 sm:w-30 sm:h-30"
                placeholder={
                  <div className="w-24 h-24 sm:w-30 sm:h-30 bg-gray-200 rounded-lg flex items-center justify-center">
                    <span className="text-gray-400 text-xs sm:text-sm">
                      圖片
                    </span>
                  </div>
                }
              />
            ) : (
              <div className="w-24 h-24 sm:w-30 sm:h-30 bg-gray-200 rounded-lg flex items-center justify-center">
                <span className="text-gray-400 text-xs sm:text-sm">無圖片</span>
              </div>
            )}
          </div>

          {/* 基本信息 */}
          <div className="flex-1 space-y-2 w-full">
            <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-3">
              <h3 className="text-base sm:text-lg font-medium text-gray-900 break-words">
                {merchantData.merchantName}
              </h3>
              <div className="flex flex-wrap gap-1 sm:gap-2">
                <Tag color={levelConfig.color} className="text-xs">
                  {levelConfig.text}
                </Tag>
                <Tag color={businessStatusConfig.color} className="text-xs">
                  {businessStatusConfig.text}
                </Tag>
                {merchantStatusConfig && (
                  <Tag color={merchantStatusConfig.color} className="text-xs">
                    {merchantStatusConfig.text}
                  </Tag>
                )}
              </div>
            </div>
            <p className="text-sm sm:text-base text-gray-600 break-words">
              {merchantData.address}
            </p>
            <div className="text-xs sm:text-sm text-gray-500">
              BD: {merchantData.bd}
            </div>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="border-t pt-3 sm:pt-4">
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-2 sm:gap-3">
            <Button
              type="default"
              size="small"
              className="bg-red-50 border-red-200 text-red-600 hover:bg-red-100 text-xs sm:text-sm"
              onClick={() => onMerchantDecoration?.(merchantData.id)}
            >
              商戶裝修
            </Button>
            <Button
              type="default"
              size="small"
              className="bg-blue-50 border-blue-200 text-blue-600 hover:bg-blue-100 text-xs sm:text-sm"
              onClick={() => onMenuManagement?.(merchantData.id)}
            >
              菜單&菜品
            </Button>
            <Button type="default" size="small" className="text-xs sm:text-sm">
              關聯任務
            </Button>
            <Button type="default" size="small" className="text-xs sm:text-sm">
              更改分配
            </Button>
            <Button type="default" size="small" className="text-xs sm:text-sm">
              帳號管理
            </Button>
            <Button type="primary" size="small" className="text-xs sm:text-sm">
              新增
            </Button>
          </div>
        </div>

        {/* 详细信息 */}
        <Descriptions
          title="詳細信息"
          bordered
          column={{ xs: 1, sm: 2 }}
          size="small"
          className="mt-3 sm:mt-4"
          styles={{
            label: { fontSize: '12px', fontWeight: '500' },
            content: { fontSize: '12px' },
          }}
        >
          <Descriptions.Item label="商戶電話1">
            {merchantData.phone1 ? (
              <span className="text-blue-600 text-xs sm:text-sm break-all">
                {merchantData.phone1}
              </span>
            ) : (
              <span className="text-gray-400 text-xs sm:text-sm">未設置</span>
            )}
          </Descriptions.Item>
          <Descriptions.Item label="商戶電話2">
            {merchantData.phone2 ? (
              <span className="text-blue-600 text-xs sm:text-sm break-all">
                {merchantData.phone2}
              </span>
            ) : (
              <span className="text-gray-400 text-xs sm:text-sm">未設置</span>
            )}
          </Descriptions.Item>
          <Descriptions.Item label="城市">
            <span className="text-xs sm:text-sm break-words">
              {merchantData.city || '--'}
            </span>
          </Descriptions.Item>
          <Descriptions.Item label="行政區">
            <span className="text-xs sm:text-sm break-words">
              {merchantData.district || '--'}
            </span>
          </Descriptions.Item>
          <Descriptions.Item label="商圈">
            <span className="text-xs sm:text-sm break-words">
              {merchantData.businessArea || '--'}
            </span>
          </Descriptions.Item>
          <Descriptions.Item label="品牌">
            <span className="text-xs sm:text-sm break-words">
              {merchantData.brand || '--'}
            </span>
          </Descriptions.Item>
          <Descriptions.Item label="統一編碼">
            <span className="text-xs sm:text-sm break-all">
              {merchantData.unifiedCode || '--'}
            </span>
          </Descriptions.Item>
          <Descriptions.Item label="加盟類型">
            <span className="text-xs sm:text-sm">
              {merchantData.franchiseType
                ? getFranchiseTypeText(merchantData.franchiseType)
                : '--'}
            </span>
          </Descriptions.Item>
          <Descriptions.Item label="營業時間">
            <span className="text-xs sm:text-sm break-words">
              {merchantData.businessHours || '--'}
            </span>
          </Descriptions.Item>
          <Descriptions.Item label="創建時間">
            <span className="text-xs sm:text-sm">
              {merchantData.createdAt || '--'}
            </span>
          </Descriptions.Item>
          <Descriptions.Item label="更新時間">
            <span className="text-xs sm:text-sm">
              {merchantData.updatedAt || '--'}
            </span>
          </Descriptions.Item>
        </Descriptions>
      </div>
    </Modal>
  );
};

export default MerchantDetailModal;
