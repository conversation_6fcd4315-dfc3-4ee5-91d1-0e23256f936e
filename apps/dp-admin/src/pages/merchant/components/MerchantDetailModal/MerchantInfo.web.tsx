import { Descriptions } from 'antd';
import React from 'react';

import type { MerchantDetailData } from './index.web';

/**
 * 商户信息展示组件属性接口
 */
export interface MerchantInfoProps {
  /** 商户数据 */
  merchantData: MerchantDetailData;
}

/**
 * 商户信息展示组件
 *
 * 用于在非编辑模式下展示商户详细信息
 */
const MerchantInfo: React.FC<MerchantInfoProps> = ({ merchantData }) => {
  // 获取加盟类型文本
  const getFranchiseTypeText = (type: string) => {
    const types = {
      DIRECT: '直營',
      FRANCHISE: '加盟',
      COOPERATION: '合作',
    };
    return types[type as keyof typeof types] || type;
  };

  return (
    <div className="space-y-4">
      {/* 详细信息表格 */}
      <Descriptions
        title="詳細信息"
        bordered
        column={{ xs: 1, sm: 2 }}
        size="small"
        className="mt-3 sm:mt-4"
        styles={{
          label: { fontSize: '12px', fontWeight: '500' },
          content: { fontSize: '12px' },
        }}
      >
        <Descriptions.Item label="商戶電話1">
          {merchantData.phone1 ? (
            <span className="text-blue-600 text-xs sm:text-sm break-all">
              {merchantData.phone1}
            </span>
          ) : (
            <span className="text-gray-400 text-xs sm:text-sm">未設置</span>
          )}
        </Descriptions.Item>

        <Descriptions.Item label="商戶電話2">
          {merchantData.phone2 ? (
            <span className="text-blue-600 text-xs sm:text-sm break-all">
              {merchantData.phone2}
            </span>
          ) : (
            <span className="text-gray-400 text-xs sm:text-sm">未設置</span>
          )}
        </Descriptions.Item>

        <Descriptions.Item label="城市">
          <span className="text-xs sm:text-sm break-words">
            {merchantData.city || '--'}
          </span>
        </Descriptions.Item>

        <Descriptions.Item label="行政區">
          <span className="text-xs sm:text-sm break-words">
            {merchantData.district || '--'}
          </span>
        </Descriptions.Item>

        <Descriptions.Item label="商圈">
          <span className="text-xs sm:text-sm break-words">
            {merchantData.businessArea || '--'}
          </span>
        </Descriptions.Item>

        <Descriptions.Item label="品牌">
          <span className="text-xs sm:text-sm break-words">
            {merchantData.brand || '--'}
          </span>
        </Descriptions.Item>

        <Descriptions.Item label="商戶類目">
          <span className="text-xs sm:text-sm break-words">
            {merchantData.category || '--'}
          </span>
        </Descriptions.Item>

        <Descriptions.Item label="統一編碼">
          <span className="text-xs sm:text-sm break-all">
            {merchantData.unifiedCode || '--'}
          </span>
        </Descriptions.Item>

        <Descriptions.Item label="加盟類型">
          <span className="text-xs sm:text-sm">
            {merchantData.franchiseType
              ? getFranchiseTypeText(merchantData.franchiseType)
              : '--'}
          </span>
        </Descriptions.Item>

        <Descriptions.Item label="營業時間">
          <span className="text-xs sm:text-sm break-words">
            {merchantData.businessHours || '--'}
          </span>
        </Descriptions.Item>

        <Descriptions.Item label="創建時間">
          <span className="text-xs sm:text-sm">
            {merchantData.createdAt || '--'}
          </span>
        </Descriptions.Item>

        <Descriptions.Item label="更新時間">
          <span className="text-xs sm:text-sm">
            {merchantData.updatedAt || '--'}
          </span>
        </Descriptions.Item>
      </Descriptions>
    </div>
  );
};

export default MerchantInfo;
