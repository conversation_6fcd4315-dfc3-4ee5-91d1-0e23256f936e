# 商户详情弹窗组件

## 📋 组件概述

商户详情弹窗组件已完成重构和功能优化，现在支持完整的编辑功能，包括标签管理和地址选择。

## 🎯 主要功能

### ✅ 已完成的功能

1. **商户信息查看**
   - 基本信息展示（店铺名称、地址、BD等）
   - 状态标签显示（等级、运营状态、商户状态）
   - 商户图片展示
   - 详细信息表格

2. **商户信息编辑**
   - 店铺名称编辑（必填）
   - 地址选择（城市 + 行政区 + 详细地址）
   - 运营状态选择
   - 商户状态选择
   - 商户类目选择
   - 联系电话编辑

3. **标签管理**
   - 查看模式：显示所有标签
   - 编辑模式：可添加和删除标签
   - 支持回车快捷键添加标签
   - 标签数量统计

4. **响应式设计**
   - 移动端和PC端适配
   - 表单字段自适应布局
   - 按钮响应式排列

## 🏗️ 组件架构

### 主组件
- `index.web.tsx` - 主弹窗组件，负责状态管理和数据流

### 子组件
- `MerchantEditForm.web.tsx` - 编辑表单组件
- `MerchantInfo.web.tsx` - 信息展示组件
- `MerchantTags.web.tsx` - 标签管理组件
- `AddressSelector.web.tsx` - 地址选择组件

## 📝 使用方式

```tsx
import MerchantDetailModal from './components/MerchantDetailModal';

// 在页面中使用
<MerchantDetailModal
  visible={merchantDetailVisible}
  merchantData={currentMerchantData}
  onCancel={handleMerchantDetailClose}
  onSave={handleSaveMerchantData}
  onMerchantDecoration={handleMerchantDecoration}
  onMenuManagement={handleMenuManagement}
/>
```

## 🔧 接口定义

```tsx
interface MerchantDetailModalProps {
  visible: boolean;
  merchantData?: MerchantDetailData;
  loading?: boolean;
  onCancel: () => void;
  onMerchantDecoration?: (merchantId: string | number) => void;
  onMenuManagement?: (merchantId: string | number) => void;
  onSave?: (merchantData: MerchantDetailData) => Promise<void>;
}

interface MerchantDetailData {
  id: string | number;
  merchantName: string;
  address: string;
  city?: string;
  district?: string;
  detailAddress?: string;
  businessStatus: string;
  merchantStatus?: string;
  level: string;
  bd: string;
  phone1?: string;
  phone2?: string;
  category?: string;
  tags?: string[];
  // ... 其他字段
}
```

## 🎨 UI特性

### 编辑模式
- 表单验证和错误提示
- 保存加载状态
- 取消编辑确认
- 地址选择器（城市 → 行政区 → 详细地址）
- 标签实时添加/删除

### 查看模式
- 清晰的信息层次
- 状态标签颜色区分
- 操作按钮分组
- 详细信息表格

## 🚀 优化特性

1. **组件抽离** - 将复杂的编辑表单拆分为独立组件
2. **标签管理** - 独立的标签组件，支持编辑和查看模式
3. **地址选择** - 专门的地址选择器，支持级联选择
4. **响应式设计** - 完整的移动端和PC端适配
5. **类型安全** - 完整的TypeScript类型定义

## 📱 响应式特性

- **移动端**：垂直布局，全宽按钮，小字体
- **平板端**：混合布局，适中间距
- **PC端**：水平布局，紧凑排列，标准字体

## 🔄 数据流

1. **查看模式** → 点击编辑 → **编辑模式**
2. **编辑模式** → 修改数据 → 保存/取消 → **查看模式**
3. **标签管理** → 实时更新 → 保存时提交
4. **地址选择** → 级联更新 → 自动组合完整地址

## 🎯 下一步优化

1. 添加图片上传功能
2. 支持更多商户类目
3. 添加操作日志记录
4. 支持批量编辑
5. 添加数据验证规则配置
