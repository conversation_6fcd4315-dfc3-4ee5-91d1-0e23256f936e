import { Input, Select } from 'antd';
import React from 'react';

/**
 * 地址选择组件属性接口
 */
export interface AddressSelectorProps {
  /** 城市值 */
  city?: string;
  /** 行政区值 */
  district?: string;
  /** 详细地址值 */
  detailAddress?: string;
  /** 城市变化回调 */
  onCityChange?: (city: string) => void;
  /** 行政区变化回调 */
  onDistrictChange?: (district: string) => void;
  /** 详细地址变化回调 */
  onDetailAddressChange?: (address: string) => void;
  /** 是否禁用 */
  disabled?: boolean;
  /** 自定义类名 */
  className?: string;
}

/**
 * 地址选择组件
 *
 * 支持城市、行政区选择和详细地址输入
 */
const AddressSelector: React.FC<AddressSelectorProps> = ({
  city,
  district,
  detailAddress,
  onCityChange,
  onDistrictChange,
  onDetailAddressChange,
  disabled = false,
  className = '',
}) => {
  // 城市选项（示例数据，实际应该从API获取）
  const cityOptions = [
    { label: '台北市', value: '台北市' },
    { label: '新北市', value: '新北市' },
    { label: '桃園市', value: '桃園市' },
    { label: '台中市', value: '台中市' },
    { label: '台南市', value: '台南市' },
    { label: '高雄市', value: '高雄市' },
    { label: '基隆市', value: '基隆市' },
    { label: '新竹市', value: '新竹市' },
    { label: '嘉義市', value: '嘉義市' },
  ];

  // 行政区选项（根据城市动态变化）
  const getDistrictOptions = (selectedCity: string) => {
    const districtMap: Record<
      string,
      Array<{ label: string; value: string }>
    > = {
      台北市: [
        { label: '中正區', value: '中正區' },
        { label: '大同區', value: '大同區' },
        { label: '中山區', value: '中山區' },
        { label: '松山區', value: '松山區' },
        { label: '大安區', value: '大安區' },
        { label: '萬華區', value: '萬華區' },
        { label: '信義區', value: '信義區' },
        { label: '士林區', value: '士林區' },
        { label: '北投區', value: '北投區' },
        { label: '內湖區', value: '內湖區' },
        { label: '南港區', value: '南港區' },
        { label: '文山區', value: '文山區' },
      ],
      新北市: [
        { label: '板橋區', value: '板橋區' },
        { label: '三重區', value: '三重區' },
        { label: '中和區', value: '中和區' },
        { label: '永和區', value: '永和區' },
        { label: '新莊區', value: '新莊區' },
        { label: '新店區', value: '新店區' },
        { label: '樹林區', value: '樹林區' },
        { label: '鶯歌區', value: '鶯歌區' },
        { label: '三峽區', value: '三峽區' },
        { label: '淡水區', value: '淡水區' },
      ],
      桃園市: [
        { label: '桃園區', value: '桃園區' },
        { label: '中壢區', value: '中壢區' },
        { label: '大溪區', value: '大溪區' },
        { label: '楊梅區', value: '楊梅區' },
        { label: '蘆竹區', value: '蘆竹區' },
        { label: '大園區', value: '大園區' },
        { label: '龜山區', value: '龜山區' },
        { label: '八德區', value: '八德區' },
        { label: '龍潭區', value: '龍潭區' },
        { label: '平鎮區', value: '平鎮區' },
      ],
      // 可以继续添加其他城市的行政区
    };

    return districtMap[selectedCity] || [];
  };

  // 处理城市变化
  const handleCityChange = (value: string) => {
    onCityChange?.(value);
    // 城市变化时清空行政区
    onDistrictChange?.('');
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {/* 城市和行政区选择 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            城市 <span className="text-red-500">*</span>
          </label>
          <Select
            placeholder="請選擇城市"
            value={city}
            onChange={handleCityChange}
            disabled={disabled}
            className="w-full"
            size="small"
            showSearch
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            options={cityOptions}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            行政區 <span className="text-red-500">*</span>
          </label>
          <Select
            placeholder="請選擇行政區"
            value={district}
            onChange={onDistrictChange}
            disabled={disabled || !city}
            className="w-full"
            size="small"
            showSearch
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            options={getDistrictOptions(city || '')}
          />
        </div>
      </div>

      {/* 详细地址输入 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          詳細地址 <span className="text-red-500">*</span>
        </label>
        <Input.TextArea
          placeholder="請輸入詳細地址（街道、門牌號等）"
          value={detailAddress}
          onChange={(e) => onDetailAddressChange?.(e.target.value)}
          disabled={disabled}
          rows={2}
          className="text-sm"
          maxLength={200}
        />
      </div>

      {/* 地址预览 */}
      {(city || district || detailAddress) && (
        <div className="p-2 bg-gray-50 rounded-md border">
          <label className="block text-xs font-medium text-gray-600 mb-1">
            完整地址預覽：
          </label>
          <div className="text-sm text-gray-800">
            {[city, district, detailAddress].filter(Boolean).join('') ||
              '請完善地址信息'}
          </div>
        </div>
      )}
    </div>
  );
};

export default AddressSelector;
