/* eslint-disable @typescript-eslint/no-explicit-any */
import { Form, Input, Select } from 'antd';
import React from 'react';

import AddressSelector from './AddressSelector.web';
import type { MerchantDetailData } from './index.web';
import MerchantTags from './MerchantTags.web';

/**
 * 商户编辑表单组件属性接口
 */
export interface MerchantEditFormProps {
  /** 表单实例 */
  form: any;
  /** 编辑中的数据 */
  editingData?: MerchantDetailData;
  /** 数据更新回调 */
  onDataChange?: (data: MerchantDetailData) => void;
  /** 添加标签回调 */
  onAddTag?: (tag: string) => void;
  /** 删除标签回调 */
  onRemoveTag?: (tag: string) => void;
}

/**
 * 商户编辑表单组件
 *
 * 包含商户基本信息编辑功能
 */
const MerchantEditForm: React.FC<MerchantEditFormProps> = ({
  form,
  editingData,
  onDataChange,
  onAddTag,
  onRemoveTag,
}) => {
  // 处理地址变化
  const handleAddressChange = (field: string, value: string) => {
    if (editingData && onDataChange) {
      const updatedData = { ...editingData, [field]: value };
      onDataChange(updatedData);

      // 同时更新表单字段
      if (
        field === 'city' ||
        field === 'district' ||
        field === 'detailAddress'
      ) {
        const fullAddress = [
          field === 'city' ? value : updatedData.city,
          field === 'district' ? value : updatedData.district,
          field === 'detailAddress' ? value : updatedData.detailAddress,
        ]
          .filter(Boolean)
          .join('');

        form.setFieldValue('address', fullAddress);
      }
    }
  };

  return (
    <Form form={form} layout="vertical" className="space-y-4">
      {/* 店铺名称 */}
      <Form.Item
        name="merchantName"
        label="店鋪名稱"
        rules={[{ required: true, message: '請輸入店鋪名稱' }]}
        className="mb-3"
      >
        <Input
          placeholder="請輸入店鋪名稱"
          className="text-sm"
          maxLength={50}
          showCount
        />
      </Form.Item>

      {/* 地址选择器 */}
      <div className="mb-3">
        <AddressSelector
          city={editingData?.city}
          district={editingData?.district}
          detailAddress={editingData?.detailAddress}
          onCityChange={(value) => handleAddressChange('city', value)}
          onDistrictChange={(value) => handleAddressChange('district', value)}
          onDetailAddressChange={(value) =>
            handleAddressChange('detailAddress', value)
          }
        />
      </div>

      {/* 隐藏的完整地址字段，用于表单验证 */}
      <Form.Item
        name="address"
        rules={[{ required: true, message: '請完善地址信息' }]}
        style={{ display: 'none' }}
      >
        <Input />
      </Form.Item>

      {/* 状态选择 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-3">
        <Form.Item
          name="businessStatus"
          label="運營狀態"
          rules={[{ required: true, message: '請選擇運營狀態' }]}
        >
          <Select placeholder="選擇運營狀態" size="small">
            <Select.Option value="OPEN">營業中</Select.Option>
            <Select.Option value="CLOSED">暫停營業</Select.Option>
            <Select.Option value="PERMANENTLY_CLOSED">永久關閉</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="merchantStatus"
          label="商戶狀態"
          rules={[{ required: true, message: '請選擇商戶狀態' }]}
        >
          <Select placeholder="選擇商戶狀態" size="small">
            <Select.Option value="APPROVED">已通過</Select.Option>
            <Select.Option value="PENDING">待審核</Select.Option>
            <Select.Option value="REJECTED">已駁回</Select.Option>
            <Select.Option value="DISABLED">已停用</Select.Option>
          </Select>
        </Form.Item>
      </div>

      {/* 商户类目 */}
      <Form.Item name="category" label="商戶類目" className="mb-3">
        <Select
          placeholder="請選擇商戶類目"
          className="text-sm"
          showSearch
          allowClear
        >
          <Select.Option value="餐飲">餐飲</Select.Option>
          <Select.Option value="零售">零售</Select.Option>
          <Select.Option value="服務">服務</Select.Option>
          <Select.Option value="娛樂">娛樂</Select.Option>
          <Select.Option value="教育">教育</Select.Option>
          <Select.Option value="醫療">醫療</Select.Option>
          <Select.Option value="美容">美容</Select.Option>
          <Select.Option value="汽車">汽車</Select.Option>
          <Select.Option value="其他">其他</Select.Option>
        </Select>
      </Form.Item>

      {/* 联系电话 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-3">
        <Form.Item
          name="phone1"
          label="商戶電話1"
          rules={[
            { pattern: /^[0-9+\-\s()]+$/, message: '請輸入有效的電話號碼' },
          ]}
        >
          <Input
            placeholder="請輸入商戶電話1"
            className="text-sm"
            maxLength={20}
          />
        </Form.Item>

        <Form.Item
          name="phone2"
          label="商戶電話2"
          rules={[
            { pattern: /^[0-9+\-\s()]+$/, message: '請輸入有效的電話號碼' },
          ]}
        >
          <Input
            placeholder="請輸入商戶電話2"
            className="text-sm"
            maxLength={20}
          />
        </Form.Item>
      </div>

      {/* 商户标签 */}
      <div className="mb-3">
        <MerchantTags
          tags={editingData?.tags}
          isEditing={true}
          onAddTag={onAddTag}
          onRemoveTag={onRemoveTag}
        />
      </div>
    </Form>
  );
};

export default MerchantEditForm;
