import React from 'react';

import BatchAssignModal from './components/BatchAssignModal/index.web';
import BatchCreateModal from './components/BatchCreateModal/index.web';
import FollowUpHistoryModal from './components/FollowUpHistoryModal/index.web';
import SearchForm from './components/SearchForm/index.web';
import TaskAssigneeModal from './components/TaskAssigneeModal/index.web';
import TaskTable from './components/TaskTable/index.web';
import MerchantDetailModal from '../components/MerchantDetailModal/index.web';
import { useTaskList } from './services/hooks';

/**
 * 商户任务列表页面组件
 *
 * 功能特性：
 * - 任务搜索与筛选
 * - 任务查看与详情
 * - 批量分配任务
 * - 批量创建任务
 * - 跟进历史查看
 * - 响应式设计，适配移动端、平板和PC端
 */
const TaskListPage: React.FC = () => {
  const {
    // 基础状态
    loading,
    data,
    total,
    currentPage,
    pageSize,

    // 表单数据
    formData,

    // 选项数据
    assigneeOptions,
    taskStatusOptions,
    taskTypeOptions,

    // 选择状态
    selectedRowKeys,
    selectedRows,

    // 模态框状态
    batchAssignVisible,
    batchCreateVisible,
    batchLoading,
    merchantDetailVisible,
    currentMerchantData,

    // 处理任务弹窗状态
    taskHandlerVisible,

    // 事件处理函数
    handleFormDataChange,
    handleSearch,
    handleReset,
    handlePageChange,
    handleRowSelectionChange,
    handleBatchAssign,
    handleBatchCreate,
    handleBatchAssignSubmit,
    handleBatchCreateSubmit,
    handleTaskDetail,
    handleTaskHandler,
    handleTaskAssignee,
    handleFollowUpHistory,
    handleMerchantDetail,
    handleCityFilter,

    // 模态框控制函数
    setBatchAssignVisible,
    setBatchCreateVisible,

    // 处理任务弹窗控制函数
    setTaskHandlerVisible,
    handleMerchantDetailClose,
  } = useTaskList();

  return (
    <div className="bg-white p-4 sm:p-6 md:p-8 rounded-lg shadow-sm">
      {/* 页面容器 */}
      <div className="mx-auto p-4 space-y-4">
        {/* 页面标题区域 */}
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <h1 className="text-xl font-semibold text-gray-900 mb-2">
            商戶任務管理
          </h1>
          <p className="text-sm text-gray-600">
            管理商戶相關任務，支持搜索篩選、批量分配和批量創建功能
          </p>
        </div>

        {/* 搜索表单区域 */}
        <SearchForm
          formData={formData}
          assigneeOptions={assigneeOptions}
          taskStatusOptions={taskStatusOptions}
          taskTypeOptions={taskTypeOptions}
          onFormDataChange={handleFormDataChange}
          onSearch={handleSearch}
          onReset={handleReset}
          onBatchAssign={handleBatchAssign}
          onBatchCreate={handleBatchCreate}
          onCityFilter={handleCityFilter}
        />

        {/* 任务表格区域 */}
        <TaskTable
          loading={loading}
          data={data}
          selectedRowKeys={selectedRowKeys}
          currentPage={currentPage}
          pageSize={pageSize}
          total={total}
          onPageChange={handlePageChange}
          onRowSelectionChange={handleRowSelectionChange}
          onTaskDetail={handleTaskDetail}
          onTaskHandler={handleTaskHandler}
          onTaskAssignee={handleTaskAssignee}
          onFollowUpHistory={handleFollowUpHistory}
          onMerchantDetail={handleMerchantDetail}
        />

        {/* 批量分配模态框 */}
        <BatchAssignModal
          visible={batchAssignVisible}
          confirmLoading={batchLoading}
          selectedTasks={selectedRows}
          assigneeOptions={assigneeOptions}
          onCancel={() => setBatchAssignVisible(false)}
          onSubmit={handleBatchAssignSubmit}
        />

        {/* 批量创建模态框 */}
        <BatchCreateModal
          visible={batchCreateVisible}
          confirmLoading={batchLoading}
          assigneeOptions={assigneeOptions}
          onCancel={() => setBatchCreateVisible(false)}
          onSubmit={handleBatchCreateSubmit}
        />

        {/* 任务分配模态框 */}
        <TaskAssigneeModal
          visible={taskHandlerVisible}
          onCancel={() => setTaskHandlerVisible(false)}
          onSave={() => {
            // TODO: 实现任务分配保存逻辑
            setTaskHandlerVisible(false);
          }}
          historyList={[]}
        />

        {/* 跟进历史模态框 */}
        <FollowUpHistoryModal />

        {/* 商户详情模态框 */}
        <MerchantDetailModal
          visible={merchantDetailVisible}
          merchantData={currentMerchantData}
          onCancel={handleMerchantDetailClose}
        />
      </div>
    </div>
  );
};

export default TaskListPage;
