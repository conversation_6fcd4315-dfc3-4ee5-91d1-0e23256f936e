/* eslint-disable @typescript-eslint/no-explicit-any */
import type { ColumnsType } from 'antd/es/table';
import React from 'react';

import CommonBatchAssignModal from '../../../components/BatchAssignModal/index.web';
import type { BatchAssignModalProps, TaskData } from '../../types';

/**
 * 批量分配任务模态框组件
 *
 * 用于批量分配任务给指定的跟进人
 */
const BatchAssignModal: React.FC<BatchAssignModalProps> = ({
  visible,
  confirmLoading,
  selectedTasks,
  assigneeOptions,
  onCancel,
  onSubmit,
}) => {
  // 任务列表表格列定义
  const taskColumns: ColumnsType<TaskData> = [
    {
      title: '任務ID',
      dataIndex: 'id',
      key: 'id',
      width: 100,
    },
    {
      title: '任務類型',
      dataIndex: 'taskType',
      key: 'taskType',
      width: 100,
    },
    {
      title: '商戶名稱',
      dataIndex: 'merchantName',
      key: 'merchantName',
      width: 200,
      render: (text: string) => (
        <span className="line-clamp-1" title={text}>
          {text}
        </span>
      ),
    },
    {
      title: '當前跟進人',
      dataIndex: 'assignee',
      key: 'assignee',
      width: 120,
      render: (text: string) => (
        <span className="text-gray-600">
          {text || <span className="text-gray-400">未分配</span>}
        </span>
      ),
    },
  ];

  // 处理提交，适配不同的数据结构
  const handleSubmit = (data: any) => {
    onSubmit({
      assigneeId: data.assigneeId,
      assigneeName: data.assigneeName,
      taskIds: data.itemIds,
    });
  };

  return (
    <CommonBatchAssignModal
      visible={visible}
      confirmLoading={confirmLoading}
      selectedItems={selectedTasks}
      assigneeOptions={assigneeOptions}
      title="批量分配任務"
      assigneeLabel="選擇跟進人"
      itemTypeName="任務"
      tableColumns={taskColumns}
      onCancel={onCancel}
      onSubmit={handleSubmit}
    />
  );
};

export default BatchAssignModal;
