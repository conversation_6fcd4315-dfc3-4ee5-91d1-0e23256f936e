/* eslint-disable @typescript-eslint/no-explicit-any */
import { Flex, Modal, Select } from 'antd';
import React from 'react';

import { CommTable } from '../../../components';
import HistoryTable from '../HistoryTable/index.web';

/**
 * 任務處理彈窗組件
 *
 * 提供任務跟進狀態更新、下次跟進時間設置和跟進詳情記錄功能
 */
const TaskAssigneeModal: React.FC<any> = ({
  visible,
  confirmLoading = false,
  taskId: _taskId,
  taskData,
  historyList,
  onCancel,
  onSave: _onSave,
}) => {
  return (
    <Modal
      open={visible}
      title="處理任務"
      width={800}
      confirmLoading={confirmLoading}
      destroyOnHidden
      onCancel={onCancel}
      footer={null}
    >
      <div className="space-y-6">
        {/* 任務基本信息 */}
        {taskData && (
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="text-sm font-medium text-gray-900 mb-2">任務信息</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">任務名稱：</span>
                <span className="text-gray-900">{taskData.taskName}</span>
              </div>
              <div>
                <span className="text-gray-600">商戶名稱：</span>
                <span className="text-gray-900">{taskData.merchantName}</span>
              </div>
            </div>
          </div>
        )}

        <div>
          <div className="mb-2">
            <Flex gap="middle">
              <div>当前跟进人: </div> <div>跟进人</div>
            </Flex>
          </div>
          <Flex gap="middle">
            <div>新跟进人: </div>{' '}
            <Select
              showSearch
              placeholder="请选择新跟进人"
              filterOption={(input, option) =>
                (option?.label ?? '')
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
              options={[
                { value: '1', label: 'Jack' },
                { value: '2', label: 'Lucy' },
                { value: '3', label: 'Tom' },
              ]}
            />
          </Flex>
        </div>

        <CommTable
          dataSource={[]}
          columns={[
            {
              title: '分配時間',
              dataIndex: 'followTime',
              key: 'followTime',
            },
            {
              title: '新跟进人',
              dataIndex: 'followTime',
              key: 'followTime',
            },
            {
              title: '分配人',
              dataIndex: 'followTime',
              key: 'followTime',
            },
          ]}
        />
        {/* 跟進歷史 */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">跟進歷史</h4>
          <HistoryTable list={historyList} />
        </div>
      </div>
    </Modal>
  );
};

export default TaskAssigneeModal;
